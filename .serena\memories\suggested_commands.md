# 建议的开发命令

## Windows系统命令
- **目录操作**: `dir`, `cd`, `mkdir`, `rmdir`
- **文件操作**: `type`, `copy`, `del`, `move`
- **搜索**: `findstr` (相当于grep)
- **进程**: `tasklist`, `taskkill`

## Python开发
- **运行主程序**: `python main.py`
- **运行测试**: `python test.py`
- **安装依赖**: `pip install -r requirements.txt` (如果有)

## MaixCAM特定
- **设备连接**: 通过USB或网络连接MaixCAM设备
- **代码部署**: 将代码传输到MaixCAM设备
- **串口调试**: 使用串口工具监控UART通信

## Git操作
- **状态检查**: `git status`
- **提交更改**: `git add .` && `git commit -m "message"`
- **推送**: `git push`
- **拉取**: `git pull`

## 调试工具
- **串口监控**: 使用串口助手或终端工具
- **图像调试**: 通过MaixCAM的显示屏实时查看处理结果
- **触摸调试**: 观察控制台输出的触摸坐标信息