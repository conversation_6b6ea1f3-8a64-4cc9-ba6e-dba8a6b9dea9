{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-28T12:59:29.073Z", "updatedAt": "2025-07-28T12:59:29.106Z", "resourceCount": 37}, "resources": [{"id": "david-data-analyst", "source": "project", "protocol": "role", "name": "<PERSON> Data Analyst 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/david-data-analyst/david-data-analyst.role.md", "metadata": {"createdAt": "2025-07-28T12:59:29.075Z", "updatedAt": "2025-07-28T12:59:29.075Z", "scannedAt": "2025-07-28T12:59:29.075Z", "path": "domain/david-data-analyst/david-data-analyst.role.md"}}, {"id": "context7-enforcement", "source": "project", "protocol": "execution", "name": "Context7 Enforcement 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/tony-tool-specialist/execution/context7-enforcement.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.101Z", "updatedAt": "2025-07-28T12:59:29.101Z", "scannedAt": "2025-07-28T12:59:29.101Z", "path": "domain/tony-tool-specialist/execution/context7-enforcement.execution.md"}}, {"id": "data-driven-analysis", "source": "project", "protocol": "execution", "name": "Data Driven Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/david-data-analyst/execution/data-driven-analysis.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.076Z", "updatedAt": "2025-07-28T12:59:29.076Z", "scannedAt": "2025-07-28T12:59:29.076Z", "path": "domain/david-data-analyst/execution/data-driven-analysis.execution.md"}}, {"id": "context7-analytics-expertise", "source": "project", "protocol": "knowledge", "name": "Context7 Analytics Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/david-data-analyst/knowledge/context7-analytics-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-28T12:59:29.078Z", "updatedAt": "2025-07-28T12:59:29.078Z", "scannedAt": "2025-07-28T12:59:29.078Z", "path": "domain/david-data-analyst/knowledge/context7-analytics-expertise.knowledge.md"}}, {"id": "data-analyst-expertise", "source": "project", "protocol": "knowledge", "name": "Data Analyst Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/david-data-analyst/knowledge/data-analyst-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-28T12:59:29.078Z", "updatedAt": "2025-07-28T12:59:29.078Z", "scannedAt": "2025-07-28T12:59:29.078Z", "path": "domain/david-data-analyst/knowledge/data-analyst-expertise.knowledge.md"}}, {"id": "data-insights", "source": "project", "protocol": "thought", "name": "Data Insights 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/david-data-analyst/thought/data-insights.thought.md", "metadata": {"createdAt": "2025-07-28T12:59:29.079Z", "updatedAt": "2025-07-28T12:59:29.079Z", "scannedAt": "2025-07-28T12:59:29.079Z", "path": "domain/david-data-analyst/thought/data-insights.thought.md"}}, {"id": "lucas-process-enforcement", "source": "project", "protocol": "execution", "name": "Lucas Process Enforcement 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/execution/lucas-process-enforcement.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.081Z", "updatedAt": "2025-07-28T12:59:29.081Z", "scannedAt": "2025-07-28T12:59:29.081Z", "path": "domain/execution/lucas-process-enforcement.execution.md"}}, {"id": "project-collaboration-workflow", "source": "project", "protocol": "execution", "name": "Project Collaboration Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/execution/project-collaboration-workflow.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.082Z", "updatedAt": "2025-07-28T12:59:29.082Z", "scannedAt": "2025-07-28T12:59:29.082Z", "path": "domain/execution/project-collaboration-workflow.execution.md"}}, {"id": "team-collaboration-guide", "source": "project", "protocol": "execution", "name": "Team Collaboration Guide 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/execution/team-collaboration-guide.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.083Z", "updatedAt": "2025-07-28T12:59:29.083Z", "scannedAt": "2025-07-28T12:59:29.083Z", "path": "domain/execution/team-collaboration-guide.execution.md"}}, {"id": "requirement-clarification", "source": "project", "protocol": "execution", "name": "Requirement Clarification 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/lucas-project-director/execution/requirement-clarification.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.084Z", "updatedAt": "2025-07-28T12:59:29.084Z", "scannedAt": "2025-07-28T12:59:29.084Z", "path": "domain/lucas-project-director/execution/requirement-clarification.execution.md"}}, {"id": "sequential-thinking-mandatory", "source": "project", "protocol": "execution", "name": "Sequential Thinking Mandatory 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/lucas-project-director/execution/sequential-thinking-mandatory.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.084Z", "updatedAt": "2025-07-28T12:59:29.084Z", "scannedAt": "2025-07-28T12:59:29.084Z", "path": "domain/lucas-project-director/execution/sequential-thinking-mandatory.execution.md"}}, {"id": "team-collaboration", "source": "project", "protocol": "execution", "name": "Team Collaboration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/lucas-project-director/execution/team-collaboration.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.086Z", "updatedAt": "2025-07-28T12:59:29.086Z", "scannedAt": "2025-07-28T12:59:29.086Z", "path": "domain/lucas-project-director/execution/team-collaboration.execution.md"}}, {"id": "project-director-expertise", "source": "project", "protocol": "knowledge", "name": "Project Director Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/lucas-project-director/knowledge/project-director-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-28T12:59:29.087Z", "updatedAt": "2025-07-28T12:59:29.087Z", "scannedAt": "2025-07-28T12:59:29.087Z", "path": "domain/lucas-project-director/knowledge/project-director-expertise.knowledge.md"}}, {"id": "<PERSON><PERSON><PERSON>-project-director", "source": "project", "protocol": "role", "name": "Lucas Project Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/lucas-project-director/lucas-project-director.role.md", "metadata": {"createdAt": "2025-07-28T12:59:29.087Z", "updatedAt": "2025-07-28T12:59:29.087Z", "scannedAt": "2025-07-28T12:59:29.087Z", "path": "domain/lucas-project-director/lucas-project-director.role.md"}}, {"id": "project-leadership", "source": "project", "protocol": "thought", "name": "Project Leadership 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/lucas-project-director/thought/project-leadership.thought.md", "metadata": {"createdAt": "2025-07-28T12:59:29.088Z", "updatedAt": "2025-07-28T12:59:29.088Z", "scannedAt": "2025-07-28T12:59:29.088Z", "path": "domain/lucas-project-director/thought/project-leadership.thought.md"}}, {"id": "architecture-design", "source": "project", "protocol": "execution", "name": "Architecture Design 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/marcus-system-architect/execution/architecture-design.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.088Z", "updatedAt": "2025-07-28T12:59:29.088Z", "scannedAt": "2025-07-28T12:59:29.088Z", "path": "domain/marcus-system-architect/execution/architecture-design.execution.md"}}, {"id": "context7-research-expertise", "source": "project", "protocol": "knowledge", "name": "Context7 Research Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/marcus-system-architect/knowledge/context7-research-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-28T12:59:29.090Z", "updatedAt": "2025-07-28T12:59:29.090Z", "scannedAt": "2025-07-28T12:59:29.090Z", "path": "domain/marcus-system-architect/knowledge/context7-research-expertise.knowledge.md"}}, {"id": "system-architect-expertise", "source": "project", "protocol": "knowledge", "name": "System Architect Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/marcus-system-architect/knowledge/system-architect-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-28T12:59:29.090Z", "updatedAt": "2025-07-28T12:59:29.090Z", "scannedAt": "2025-07-28T12:59:29.090Z", "path": "domain/marcus-system-architect/knowledge/system-architect-expertise.knowledge.md"}}, {"id": "marcus-system-architect", "source": "project", "protocol": "role", "name": "Marcus System Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/marcus-system-architect/marcus-system-architect.role.md", "metadata": {"createdAt": "2025-07-28T12:59:29.091Z", "updatedAt": "2025-07-28T12:59:29.091Z", "scannedAt": "2025-07-28T12:59:29.091Z", "path": "domain/marcus-system-architect/marcus-system-architect.role.md"}}, {"id": "system-architecture", "source": "project", "protocol": "thought", "name": "System Architecture 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/marcus-system-architect/thought/system-architecture.thought.md", "metadata": {"createdAt": "2025-07-28T12:59:29.091Z", "updatedAt": "2025-07-28T12:59:29.091Z", "scannedAt": "2025-07-28T12:59:29.091Z", "path": "domain/marcus-system-architect/thought/system-architecture.thought.md"}}, {"id": "architectural-constraints", "source": "project", "protocol": "execution", "name": "Architectural Constraints 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ryan-fullstack-engineer/execution/architectural-constraints.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.092Z", "updatedAt": "2025-07-28T12:59:29.092Z", "scannedAt": "2025-07-28T12:59:29.092Z", "path": "domain/ryan-fullstack-engineer/execution/architectural-constraints.execution.md"}}, {"id": "tdd-workflow", "source": "project", "protocol": "execution", "name": "Tdd Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ryan-fullstack-engineer/execution/tdd-workflow.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.094Z", "updatedAt": "2025-07-28T12:59:29.094Z", "scannedAt": "2025-07-28T12:59:29.094Z", "path": "domain/ryan-fullstack-engineer/execution/tdd-workflow.execution.md"}}, {"id": "context7-development-expertise", "source": "project", "protocol": "knowledge", "name": "Context7 Development Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ryan-fullstack-engineer/knowledge/context7-development-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-28T12:59:29.095Z", "updatedAt": "2025-07-28T12:59:29.095Z", "scannedAt": "2025-07-28T12:59:29.095Z", "path": "domain/ryan-fullstack-engineer/knowledge/context7-development-expertise.knowledge.md"}}, {"id": "fullstack-engineering-expertise", "source": "project", "protocol": "knowledge", "name": "Fullstack Engineering Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ryan-fullstack-engineer/knowledge/fullstack-engineering-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-28T12:59:29.096Z", "updatedAt": "2025-07-28T12:59:29.096Z", "scannedAt": "2025-07-28T12:59:29.096Z", "path": "domain/ryan-fullstack-engineer/knowledge/fullstack-engineering-expertise.knowledge.md"}}, {"id": "<PERSON><PERSON>-fullstack-engineer", "source": "project", "protocol": "role", "name": "<PERSON> Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/ryan-fullstack-engineer/ryan-fullstack-engineer.role.md", "metadata": {"createdAt": "2025-07-28T12:59:29.096Z", "updatedAt": "2025-07-28T12:59:29.096Z", "scannedAt": "2025-07-28T12:59:29.096Z", "path": "domain/ryan-fullstack-engineer/ryan-fullstack-engineer.role.md"}}, {"id": "engineering-excellence", "source": "project", "protocol": "thought", "name": "Engineering Excellence 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ryan-fullstack-engineer/thought/engineering-excellence.thought.md", "metadata": {"createdAt": "2025-07-28T12:59:29.097Z", "updatedAt": "2025-07-28T12:59:29.097Z", "scannedAt": "2025-07-28T12:59:29.097Z", "path": "domain/ryan-fullstack-engineer/thought/engineering-excellence.thought.md"}}, {"id": "context7-priority", "source": "project", "protocol": "execution", "name": "Context7 Priority 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/shared/execution/context7-priority.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.097Z", "updatedAt": "2025-07-28T12:59:29.097Z", "scannedAt": "2025-07-28T12:59:29.097Z", "path": "domain/shared/execution/context7-priority.execution.md"}}, {"id": "prd-workflow", "source": "project", "protocol": "execution", "name": "Prd Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/sophia-product-strategist/execution/prd-workflow.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.098Z", "updatedAt": "2025-07-28T12:59:29.098Z", "scannedAt": "2025-07-28T12:59:29.098Z", "path": "domain/sophia-product-strategist/execution/prd-workflow.execution.md"}}, {"id": "context7-feasibility-expertise", "source": "project", "protocol": "knowledge", "name": "Context7 Feasibility Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/sophia-product-strategist/knowledge/context7-feasibility-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-28T12:59:29.099Z", "updatedAt": "2025-07-28T12:59:29.099Z", "scannedAt": "2025-07-28T12:59:29.099Z", "path": "domain/sophia-product-strategist/knowledge/context7-feasibility-expertise.knowledge.md"}}, {"id": "product-strategy-expertise", "source": "project", "protocol": "knowledge", "name": "Product Strategy Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/sophia-product-strategist/knowledge/product-strategy-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-28T12:59:29.100Z", "updatedAt": "2025-07-28T12:59:29.100Z", "scannedAt": "2025-07-28T12:59:29.100Z", "path": "domain/sophia-product-strategist/knowledge/product-strategy-expertise.knowledge.md"}}, {"id": "sophia-product-strategist", "source": "project", "protocol": "role", "name": "Sophia Product Strategist 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/sophia-product-strategist/sophia-product-strategist.role.md", "metadata": {"createdAt": "2025-07-28T12:59:29.100Z", "updatedAt": "2025-07-28T12:59:29.100Z", "scannedAt": "2025-07-28T12:59:29.100Z", "path": "domain/sophia-product-strategist/sophia-product-strategist.role.md"}}, {"id": "product-thinking", "source": "project", "protocol": "thought", "name": "Product Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/sophia-product-strategist/thought/product-thinking.thought.md", "metadata": {"createdAt": "2025-07-28T12:59:29.101Z", "updatedAt": "2025-07-28T12:59:29.101Z", "scannedAt": "2025-07-28T12:59:29.101Z", "path": "domain/sophia-product-strategist/thought/product-thinking.thought.md"}}, {"id": "mcp-tools-expertise", "source": "project", "protocol": "knowledge", "name": "Mcp Tools Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/tony-tool-specialist/knowledge/mcp-tools-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-28T12:59:29.102Z", "updatedAt": "2025-07-28T12:59:29.102Z", "scannedAt": "2025-07-28T12:59:29.102Z", "path": "domain/tony-tool-specialist/knowledge/mcp-tools-expertise.knowledge.md"}}, {"id": "tony-tool-specialist", "source": "project", "protocol": "role", "name": "<PERSON> Specialist 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/tony-tool-specialist/tony-tool-specialist.role.md", "metadata": {"createdAt": "2025-07-28T12:59:29.103Z", "updatedAt": "2025-07-28T12:59:29.103Z", "scannedAt": "2025-07-28T12:59:29.103Z", "path": "domain/tony-tool-specialist/tony-tool-specialist.role.md"}}, {"id": "process-enforcement", "source": "project", "protocol": "execution", "name": "Process Enforcement 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/process-supervisor/execution/process-enforcement.execution.md", "metadata": {"createdAt": "2025-07-28T12:59:29.104Z", "updatedAt": "2025-07-28T12:59:29.104Z", "scannedAt": "2025-07-28T12:59:29.104Z", "path": "role/process-supervisor/execution/process-enforcement.execution.md"}}, {"id": "process-supervisor", "source": "project", "protocol": "role", "name": "Process Supervisor 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/process-supervisor/process-supervisor.role.md", "metadata": {"createdAt": "2025-07-28T12:59:29.104Z", "updatedAt": "2025-07-28T12:59:29.104Z", "scannedAt": "2025-07-28T12:59:29.104Z", "path": "role/process-supervisor/process-supervisor.role.md"}}, {"id": "process-monitoring", "source": "project", "protocol": "thought", "name": "Process Monitoring 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/process-supervisor/thought/process-monitoring.thought.md", "metadata": {"createdAt": "2025-07-28T12:59:29.105Z", "updatedAt": "2025-07-28T12:59:29.105Z", "scannedAt": "2025-07-28T12:59:29.105Z", "path": "role/process-supervisor/thought/process-monitoring.thought.md"}}], "stats": {"totalResources": 37, "byProtocol": {"role": 7, "execution": 14, "knowledge": 10, "thought": 6}, "bySource": {"project": 37}}}