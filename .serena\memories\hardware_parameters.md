# 极光车融合版 - 硬件参数配置文档

## 📷 摄像头参数

### 主程序摄像头配置 (main.py)
```python
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
```
- **分辨率**: 320×240 像素
- **颜色格式**: BGR888 (24位真彩色)
- **用途**: 主要图像处理和矩形检测
- **备注**: 注释中提到"使用初版的高分辨率"

### 测试程序摄像头配置 (test.py)
```python
cam = camera.Camera(640, 480)
```
- **分辨率**: 640×480 像素 (VGA)
- **颜色格式**: 默认格式
- **用途**: 测试和调试

## 🖥️ 显示屏参数

### 显示器配置
```python
disp = display.Display()
```
- **类型**: MaixCAM内置显示屏
- **初始化**: 使用默认参数
- **显示内容**: 实时图像处理结果、虚拟按键界面
- **坐标系**: 左上角为原点(0,0)

### 显示区域定义
- **图像显示区域**: 全屏显示摄像头画面
- **虚拟按键区域**: 屏幕下方 (y=180-200)
- **信息显示区域**: 屏幕上方 (y=20-60)

## 🖱️ 触摸屏参数

### 触摸屏配置
```python
ts = touchscreen.TouchScreen()
```
- **类型**: MaixCAM内置电容触摸屏
- **初始化**: 无参数，使用默认配置
- **坐标范围**: 与显示屏坐标系一致

### 触摸检测区域
```python
self.touch_areas = [
    [70, 265, 100, 40],     # Center按钮区域
    [230, 265, 90, 40],     # Circle按钮区域  
    [330, 265, 50, 40],     # T-按钮区域
    [390, 265, 50, 40]      # T+按钮区域
]
```

### 触摸参数
- **防抖时间**: 300ms (0.3秒)
- **检测方式**: 按下事件触发
- **坐标精度**: 像素级精度

## 📡 串口通信参数

### UART配置
```python
uart = SimpleUART()
uart.init("/dev/ttyS0", 115200, set_as_global=True)
```

#### 基本参数
- **设备路径**: `/dev/ttyS0`
- **波特率**: 115200 bps
- **数据位**: 8位 (默认)
- **停止位**: 1位 (默认)
- **校验位**: 无 (默认)

#### 帧格式配置
```python
uart.set_frame("$$", "##", True)
```
- **帧头**: `$$`
- **帧尾**: `##`
- **帧格式**: `$$数据内容##`
- **启用状态**: True

#### 缓冲区配置
- **最大缓冲区大小**: 1024 字节
- **刷新间隔**: 500ms

## 🎯 图像处理参数

### 矩形检测参数
```python
min_contour_area = 500      # 最小轮廓面积
max_contour_area = 70000    # 最大轮廓面积
target_sides = 4            # 目标边数(矩形)
binary_threshold = 66       # 二值化阈值(可调)
```

### 透视变换参数
```python
corrected_width = 200       # 校正后矩形宽度
corrected_height = 150      # 校正后矩形高度
circle_radius = 50          # 圆形轨迹半径
circle_num_points = 12      # 圆周点数量
```

### 激光检测参数 (注释状态)
```python
pixel_radius = 3            # 像素半径
kernel = np.ones((3, 3), np.uint8)  # 形态学核
# HSV颜色范围 (紫色激光)
lower_purple = [130, 80, 80]
upper_purple = [160, 255, 255]
```

## 🔧 系统性能参数

### FPS计算
- **计算方式**: 基于帧间时间差
- **显示位置**: 屏幕左上角
- **更新频率**: 实时更新

### 内存管理
```python
gc.disable()  # 禁用垃圾回收以提高实时性
```

### 目标标记点
```python
target_x, target_y = 150, 95  # 固定目标点坐标
cross_size = 5                # 十字标记大小
```

## 📊 数据通信协议

### Center模式数据格式
```
R,cx,cy
```
- **R**: 模式标识符
- **cx,cy**: 中心点坐标

### Circle模式数据格式  
```
C,点数,x1,y1,x2,y2,...
```
- **C**: 模式标识符
- **点数**: 圆周点的数量
- **x1,y1...**: 各点坐标序列

## 🛠️ 硬件兼容性

### 支持平台
- **主控**: MaixCAM开发板
- **操作系统**: MaixPy环境
- **Python版本**: 基于MaixPy的Python实现

### 外设接口
- **摄像头接口**: CSI摄像头接口
- **显示接口**: 内置LCD显示屏
- **触摸接口**: 电容触摸屏
- **串口接口**: UART0 (/dev/ttyS0)