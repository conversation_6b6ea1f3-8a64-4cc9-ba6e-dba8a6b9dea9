# 代码风格和约定

## 命名约定
- **类名**: 使用PascalCase (如: PurpleLaserDetector, VirtualButtons)
- **函数名**: 使用snake_case (如: init_touchscreen, generate_circle_points)
- **变量名**: 使用snake_case (如: current_mode, binary_threshold)
- **常量**: 使用UPPER_CASE (如: 虽然代码中未严格遵循)

## 代码结构
- **类定义**: 每个功能模块封装为独立的类
- **函数组织**: 工具函数独立定义，主程序逻辑清晰
- **注释风格**: 使用中文注释，详细说明功能模块

## 设计模式
- **模块化设计**: 激光检测、虚拟按键、图像处理分离
- **状态管理**: 使用简单的状态变量管理模式切换
- **事件驱动**: 触摸事件驱动的交互模式

## 错误处理
- 使用try-except包装可能失败的操作
- 提供备用方案（如透视变换失败时的简单几何中心计算）
- 定期打印错误信息而非每次都打印