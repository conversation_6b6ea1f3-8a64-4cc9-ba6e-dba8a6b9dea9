# 极光车融合版 - 阈值调节指南

## 📋 概述

本文档详细介绍了极光车融合版项目中所有可调节的阈值参数，包括调节方法、作用效果和优化建议。

---

## 🎯 1. 二值化阈值 (binary_threshold)

### 基本信息
- **参数名称**: `binary_threshold`
- **默认值**: `66`
- **调节范围**: 1-255
- **调节步长**: ±3
- **调节方式**: **触屏实时调节**

### 触屏操作
- **T+ 按钮**: 阈值增加3，位置 [210, 180, 25, 20]
- **T- 按钮**: 阈值减少3，位置 [180, 180, 25, 20]
- **实时显示**: 屏幕显示 `Thresh: 66`

### 作用与效果
- **功能**: 控制图像二值化的阈值，影响矩形检测的敏感度
- **增加阈值** (T+):
  - ✅ 更严格的边缘检测
  - ✅ 减少噪点干扰
  - ❌ 可能漏检暗色矩形
  - 适用场景：光线充足，噪点较多
- **减少阈值** (T-):
  - ✅ 更宽松的边缘检测
  - ✅ 增加检测敏感度
  - ❌ 可能误检噪点
  - 适用场景：光线较暗，矩形对比度低

### 调节建议
| 问题现象 | 调节方向 | 建议值范围 |
|---------|---------|-----------|
| 检测不到矩形 | 降低 (T-) | 40-60 |
| 误检噪点太多 | 提高 (T+) | 70-90 |
| 光线充足环境 | 适当提高 | 60-80 |
| 光线较暗环境 | 适当降低 | 45-65 |

---

## 🔲 2. 轮廓面积阈值

### 最小面积阈值 (min_contour_area)
- **参数名称**: `min_contour_area`
- **默认值**: `500`
- **调节方式**: 修改代码第218行
- **作用**: 过滤掉过小的轮廓，避免误检小噪点

#### 调节效果
- **减小值** (如300):
  - ✅ 可检测更小的矩形
  - ❌ 可能增加小噪点误检
- **增大值** (如800):
  - ✅ 只检测较大的矩形
  - ❌ 可能漏检小矩形

### 最大面积阈值 (max_contour_area)
- **参数名称**: `max_contour_area`
- **默认值**: `70000`
- **调节方式**: 修改代码第219行
- **作用**: 过滤掉过大的轮廓，避免误检整个画面

#### 调节效果
- **减小值** (如50000):
  - ✅ 过滤掉超大轮廓
  - ❌ 可能漏检大矩形
- **增大值** (如100000):
  - ✅ 允许检测更大的矩形
  - ❌ 可能误检背景

### 面积阈值调节建议
| 矩形大小 | min_area | max_area | 适用场景 |
|---------|----------|----------|----------|
| 小矩形 | 200-400 | 50000 | 近距离检测 |
| 中矩形 | 500-800 | 70000 | 标准距离 |
| 大矩形 | 1000+ | 100000+ | 远距离检测 |

---

## 📐 3. 多边形近似精度 (epsilon)

### 基本信息
- **参数表达式**: `epsilon = 0.03 * cv2.arcLength(cnt, True)`
- **默认系数**: `0.03`
- **调节方式**: 修改代码第291行的系数
- **作用**: 控制轮廓近似为多边形的精度

### 调节效果
- **减小系数** (如0.02):
  - ✅ 更精确的多边形近似
  - ❌ 可能检测到5边形、6边形等
  - 适用：形状规整的矩形
- **增大系数** (如0.04):
  - ✅ 更粗糙的近似，更容易得到4边形
  - ❌ 可能丢失细节
  - 适用：形状不规整的矩形

### 精度调节建议
| 矩形特征 | 建议系数 | 效果 |
|---------|----------|------|
| 边缘清晰 | 0.02-0.025 | 精确检测 |
| 标准情况 | 0.03 | 平衡效果 |
| 边缘模糊 | 0.035-0.04 | 容错检测 |

---

## 🎨 4. 激光检测HSV阈值 (已注释)

### 基本信息
- **状态**: 当前已注释，需要时可启用
- **位置**: main.py 第19-21行
- **颜色目标**: 紫色激光点

### HSV参数
```python
lower_purple = np.array([130, 80, 80])   # HSV下限
upper_purple = np.array([160, 255, 255]) # HSV上限
```

### 参数说明
- **色调 (H)**: 130-160 (紫色范围)
- **饱和度 (S)**: 80-255 (中等到高饱和度)
- **亮度 (V)**: 80-255 (中等到高亮度)

### 调节建议
| 检测问题 | 调节参数 | 建议值 |
|---------|----------|--------|
| 检测不到激光 | 扩大H范围 | [120, 170] |
| 误检其他紫色 | 缩小H范围 | [135, 155] |
| 光线太暗 | 降低V下限 | [50, 255] |
| 光线太亮 | 提高V下限 | [100, 255] |

---

## 🔄 5. 透视变换参数

### 基本参数
- **校正宽度**: `corrected_width = 200`
- **校正高度**: `corrected_height = 150`
- **圆形半径**: `circle_radius = 50`
- **圆周点数**: `circle_num_points = 12`

### 调节位置
- 代码第224-227行

### 参数作用
- **corrected_width/height**: 透视变换后的标准矩形尺寸
- **circle_radius**: Circle模式下圆形轨迹的半径
- **circle_num_points**: 圆形轨迹的点数量

---

## 🛠️ 快速调节指南

### 常见问题与解决方案

#### 🔍 检测问题
| 问题 | 主要调节参数 | 操作方法 |
|------|-------------|----------|
| 检测不到矩形 | binary_threshold | 触屏T-按钮降低 |
| 误检噪点 | binary_threshold | 触屏T+按钮提高 |
| 小矩形漏检 | min_contour_area | 代码中减小值 |
| 大物体误检 | max_contour_area | 代码中减小值 |
| 形状不够方正 | epsilon系数 | 代码中调节 |

#### 🌟 环境适配
| 环境条件 | binary_threshold | 其他建议 |
|---------|-----------------|----------|
| 强光环境 | 70-90 | 可适当提高面积阈值 |
| 弱光环境 | 40-60 | 可适当降低面积阈值 |
| 室内标准光 | 60-75 | 使用默认面积阈值 |
| 户外阳光 | 75-95 | 注意阴影干扰 |

---

## 📝 调节记录模板

```
调节日期: ____
环境条件: ____
问题描述: ____

调节参数:
- binary_threshold: 从 __ 调至 __
- min_contour_area: 从 __ 调至 __
- max_contour_area: 从 __ 调至 __
- epsilon系数: 从 __ 调至 __

调节效果: ____
备注: ____
```

---

## ⚠️ 注意事项

1. **触屏调节**: 只有binary_threshold支持实时触屏调节
2. **代码修改**: 其他参数需要修改代码并重新运行
3. **参数范围**: 注意各参数的有效范围，避免极端值
4. **环境测试**: 不同光线条件下需要重新调节
5. **备份设置**: 记录有效的参数组合以便恢复

---

*文档版本: v1.0*  
*更新日期: 2025-01-31*  
*适用版本: 极光车融合版*
