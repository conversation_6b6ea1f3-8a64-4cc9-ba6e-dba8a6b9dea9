# 模式切换机制详解

## 两种工作模式

### 1. Center模式（中心点模式）
- **功能**: 检测矩形并计算其几何中心点
- **数据输出**: 发送单个中心点坐标 `R,cx,cy`
- **应用场景**: 目标跟踪、定点控制

### 2. Circle模式（圆形轨迹模式）
- **功能**: 在检测到的矩形内生成圆形轨迹点
- **数据输出**: 发送圆周上的多个坐标点 `C,点数,x1,y1,x2,y2,...`
- **应用场景**: 轨迹规划、路径跟踪

## 模式切换实现

### 触摸屏控制
- **Center按钮**: 位置[70,265,100,40] - 切换到中心点模式
- **Circle按钮**: 位置[230,265,90,40] - 切换到圆形模式
- **防抖机制**: 300ms防抖动时间
- **视觉反馈**: 当前激活模式按钮显示黄色高亮

### 代码实现关键点
```python
# 模式状态变量
current_mode = "center"  # 默认中心点模式

# 触摸事件处理
if action == "center":
    current_mode = "center"
elif action == "circle":
    current_mode = "circle"

# 数据发送逻辑
if current_mode == "center":
    micu_printf(f"R,{cx},{cy}")
elif current_mode == "circle":
    circle_data = f"C,{len(all_circle_points)}"
    for (x, y) in all_circle_points:
        circle_data += f",{x},{y}"
    micu_printf(circle_data)
```