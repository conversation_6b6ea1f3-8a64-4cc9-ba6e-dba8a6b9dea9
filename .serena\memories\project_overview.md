# 极光车融合版项目概述

## 项目目的
这是一个基于MaixCAM的智能车控制系统，主要功能包括：
- 矩形检测和跟踪
- 两种工作模式：中心点模式和圆形轨迹模式
- 触摸屏交互控制
- 串口通信
- 实时图像处理

## 技术栈
- **硬件平台**: MaixCAM
- **编程语言**: Python
- **主要库**: 
  - maix (MaixCAM SDK)
  - OpenCV (cv2)
  - NumPy
  - 自定义串口库 (micu_uart_lib)

## 核心功能
1. **图像处理**: 使用OpenCV进行矩形检测、二值化处理
2. **模式切换**: 支持center和circle两种工作模式
3. **触摸控制**: 虚拟按键系统，支持模式切换和参数调整
4. **串口通信**: 通过UART发送检测结果
5. **透视变换**: 矫正检测到的矩形，提供精确的几何计算